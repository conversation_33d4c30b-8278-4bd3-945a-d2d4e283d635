import { ObjectType, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.input';

export enum UserRoles {
  ADMIN = 'ADMIN',
  USER = 'USER',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

registerEnumType(UserRoles, { name: 'UserRoles' });
registerEnumType(UserStatus, { name: 'UserStatus' });

@ObjectType()
export class Contact {
  /** Country code (e.g., +1, +91) */
  countryCode: string;

  /** Phone number */
  phone: string;
}

@ObjectType()
@Schema()
export class User extends MongooseSchema {
  /** User fullname */
  @Prop({ required: true })
  fullname: string;

  /** User's email */
  @Prop({ required: true, unique: true })
  email: string;

  /** User active status */
  @Prop({ required: true, default: UserStatus.ACTIVE, enum: UserStatus })
  userStatus: UserStatus;

  /** User role */
  @Prop({ required: true, enum: Object.values(UserRoles) })
  role: UserRoles;

  @Prop({ required: true })
  password: string;

  /** User contact information */
  @Prop({ required: true, type: Object })
  contact: Contact;

  /** Whether user has accepted terms and conditions */
  @Prop({ required: true })
  acceptedTermsAndConditions: boolean;

  /** Whether user wants to receive discounts, royalty offers and updates */
  @Prop({ required: true })
  subscribeToUpdates: boolean;
}

@ObjectType()
export class PaginatedUsers extends createPaginatedType(User) {}

export const UserSchema = SchemaFactory.createForClass(User);
