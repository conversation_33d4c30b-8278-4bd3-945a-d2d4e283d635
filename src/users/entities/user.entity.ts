import { ObjectType, registerEnumType, Field } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.input';

export enum UserRoles {
  ADMIN = 'ADMIN',
  USER = 'USER',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

registerEnumType(UserRoles, { name: 'UserRoles' });
registerEnumType(UserStatus, { name: 'UserStatus' });

@ObjectType()
export class Contact {
  /** Country code (e.g., +1, +91) */
  @Field()
  countryCode: string;

  /** Phone number */
  @Field()
  phone: string;
}

@ObjectType()
@Schema()
export class User extends MongooseSchema {
  /** User fullname */
  @Field()
  @Prop({ required: true })
  fullname: string;

  /** User's email */
  @Field()
  @Prop({ required: true, unique: true })
  email: string;

  /** User active status */
  @Field(() => UserStatus)
  @Prop({ required: true, default: UserStatus.ACTIVE, enum: UserStatus })
  userStatus: UserStatus;

  /** User role */
  @Field(() => UserRoles)
  @Prop({ required: true, enum: Object.values(UserRoles) })
  role: UserRoles;

  @Field()
  @Prop({ required: true })
  password: string;

  /** User contact information */
  @Field()
  @Prop({ required: true, type: Object })
  contact: Contact;

  /** Whether user has accepted terms and conditions */
  @Field()
  @Prop({ required: true })
  acceptedTermsAndConditions: boolean;

  /** Whether user wants to receive discounts, royalty offers and updates */
  @Field()
  @Prop({ required: true })
  subscribeToUpdates: boolean;
}

@ObjectType()
export class PaginatedUsers extends createPaginatedType(User) {}

export const UserSchema = SchemaFactory.createForClass(User);
