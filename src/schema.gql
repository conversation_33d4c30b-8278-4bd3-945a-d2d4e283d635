# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Coordinates {
  """Latitude"""
  latitude: Float!

  """Longitude"""
  longitude: Float!
}

type Address {
  """address"""
  address: String!

  """Coordinates"""
  coordinates: Coordinates!

  """Metro line"""
  metroLine: String

  """Metro station"""
  metroStation: String
}

type Contact {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

type User {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!

  """user fullname"""
  fullname: String!

  """user's email"""
  email: String!

  """user active status"""
  userStatus: UserStatus!

  """user role"""
  role: UserRoles!

  """User contact information"""
  contact: Contact!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
  password: String!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

enum UserStatus {
  ACTIVE
  INACTIVE
}

enum UserRoles {
  ADMIN
  USER
}

type PaginatedUsers {
  """Array of documents"""
  docs: [User!]!

  """Total number of documents"""
  totalDocs: Int!

  """Number of documents per page"""
  limit: Int!

  """Current page number"""
  page: Int!

  """Total number of pages"""
  totalPages: Int!

  """Previous page number"""
  prevPage: Int

  """Next page number"""
  nextPage: Int

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Int
}

type AuthOutput {
  """Access token"""
  access_token: String!
}

type PresignedFields {
  key: String!
  bucket: String!
  acl: String!
  algorithm: String!
  credential: String!
  date: String!
  Policy: String!
  signature: String!
}

type SignedUploadUrl {
  url: String!
  fields: PresignedFields!
}

type ClubCategory {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!

  """Club Category name"""
  name: String!

  """Club Category description"""
  description: String!
}

type DayTiming {
  """Day of the week"""
  day: DayOfWeek!

  """
  Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30)
  """
  timings: [Float!]!
}

enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

type OpeningHours {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTiming!]!
}

type Club {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!

  """Club name"""
  name: String!

  """Club description"""
  description: String!

  """Club category"""
  categories: [ClubCategory!]!

  """Club status"""
  status: ClubStatus!

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]!

  """Club phone number"""
  phone: Contact

  """Club address"""
  address: Address

  """Opening hours"""
  openingHours: OpeningHours

  """Whether club is featured"""
  featured: Boolean!

  """Club rating (0-5)"""
  rating: Float!
}

enum ClubStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

type PaginatedClubs {
  """Array of documents"""
  docs: [Club!]!

  """Total number of documents"""
  totalDocs: Int!

  """Number of documents per page"""
  limit: Int!

  """Current page number"""
  page: Int!

  """Total number of pages"""
  totalPages: Int!

  """Previous page number"""
  prevPage: Int

  """Next page number"""
  nextPage: Int

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Int
}

type City {
  """Example field (placeholder)"""
  exampleField: Int!
}

type Query {
  """Logged in  user"""
  me: User!
  users(usersInput: UsersInput): PaginatedUsers!
  user(id: String!): User!
  clubs(clubsInput: ClubsInput): PaginatedClubs!
  club(id: String!): Club!
  city(id: Int!): City!
}

input UsersInput {
  """Page number (1-based)"""
  page: Int = 1

  """Number of items per page"""
  limit: Int = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!] = {}

  """Pagination options"""
  pagination: PaginationInput
}

input SortConfigInput {
  """Field to sort by"""
  field: String!

  """
  Sort order: "asc" or "desc"
  """
  order: String! = "desc"
}

input PaginationInput {
  """Page number (1-based)"""
  page: Int = 1

  """Number of items per page"""
  limit: Int = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!] = {}
}

input ClubsInput {
  """Page number (1-based)"""
  page: Int = 1

  """Number of items per page"""
  limit: Int = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!] = {}

  """Pagination options"""
  pagination: PaginationInput
}

type Mutation {
  signIn(input: SignInInput!): AuthOutput!
  signUp(input: CreateUserInput!): AuthOutput!
  createSignedUploadUrl(input: SignedUploadUrlInput!): SignedUploadUrl!
  createClub(createClubInput: CreateClubInput!): Boolean!
  updateClub(updateClubInput: UpdateClubInput!): Club!
  removeClub(id: String!): Club!
  createCity(createCityInput: CreateCityInput!): City!
  updateCity(updateCityInput: UpdateCityInput!): City!
  removeCity(id: Int!): City!
}

input SignInInput {
  """user phone number"""
  email: String!

  """user password"""
  password: String!
}

input CreateUserInput {
  """user fullname"""
  fullname: String!

  """user phone number"""
  email: String!

  """user role"""
  role: UserRoles!

  """user password"""
  password: String!

  """user active status"""
  userStatus: UserStatus!

  """User contact information"""
  contact: ContactInput!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}

input ContactInput {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

input SignedUploadUrlInput {
  key: String!
  contentType: String!
  expiresIn: Float
}

input CreateClubInput {
  """Club name"""
  name: String!

  """Club description"""
  description: String!

  """Club category IDs"""
  categories: [String!]!

  """Club status"""
  status: ClubStatus!

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]!

  """Club contact information (phone)"""
  contact: ContactInput

  """Club address"""
  address: AddressInput

  """Opening hours"""
  openingHours: OpeningHoursInput

  """Whether club is featured"""
  featured: Boolean!

  """Club rating (0-5)"""
  rating: Float!
}

input AddressInput {
  """Address"""
  address: String!

  """Coordinates"""
  coordinates: CoordinatesInput!

  """Metro line"""
  metroLine: String

  """Metro station"""
  metroStation: String
}

input CoordinatesInput {
  """Latitude"""
  latitude: Float!

  """Longitude"""
  longitude: Float!
}

input OpeningHoursInput {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTimingInput!]!
}

input DayTimingInput {
  """Day of the week"""
  day: DayOfWeek!

  """
  Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc.
  """
  timings: [Float!]!
}

input UpdateClubInput {
  """Club name"""
  name: String

  """Club description"""
  description: String

  """Club category IDs"""
  categories: [String!]

  """Club status"""
  status: ClubStatus

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]

  """Club contact information (phone)"""
  contact: ContactInput

  """Club address"""
  address: AddressInput

  """Opening hours"""
  openingHours: OpeningHoursInput

  """Whether club is featured"""
  featured: Boolean

  """Club rating (0-5)"""
  rating: Float
  id: String!
}

input CreateCityInput {
  """Example field (placeholder)"""
  exampleField: Int!
}

input UpdateCityInput {
  """Example field (placeholder)"""
  exampleField: Int
  id: Int!
}