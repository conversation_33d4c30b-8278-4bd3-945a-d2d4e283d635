# Mongoose Paginate V2 Implementation Guide

## Overview

This implementation provides a complete pagination solution using `mongoose-paginate-v2` with GraphQL support.

## Features

- ✅ Global mongoose plugin configuration
- ✅ GraphQL-compatible pagination DTOs
- ✅ Type-safe pagination inputs and outputs
- ✅ Flexible sorting options
- ✅ Validation with class-validator
- ✅ Reusable pagination types

## Usage Examples

### 1. GraphQL Query Examples

#### Basic Pagination Query
```graphql
query GetUsersPaginated {
  usersPaginated(usersInput: {
    pagination: {
      page: 1
      limit: 10
    }
  }) {
    docs {
      id
      fullname
      email
      role
      userStatus
    }
    pagination {
      totalDocs
      limit
      page
      totalPages
      hasNextPage
      hasPrevPage
      nextPage
      prevPage
    }
  }
}
```

#### Pagination with Filtering and Sorting
```graphql
query GetUsersPaginatedWithFilters {
  usersPaginated(usersInput: {
    roles: [USER]
    pagination: {
      page: 2
      limit: 5
      sort: "createdAt"
      sortOrder: "desc"
    }
  }) {
    docs {
      id
      fullname
      email
      role
      createdAt
    }
    pagination {
      totalDocs
      totalPages
      page
      hasNextPage
      hasPrevPage
    }
  }
}
```

### 2. Service Implementation Pattern

```typescript
// In your service
async findAllPaginated(
  filter: FilterQuery<YourEntity> = {},
  paginationInput?: PaginationInput,
): Promise<PaginatedYourEntity> {
  const options: any = {
    page: paginationInput?.page || 1,
    limit: paginationInput?.limit || 10,
    sort: {},
  };

  // Handle sorting
  if (paginationInput?.sort) {
    const sortOrder = paginationInput.sortOrder === 'asc' ? 1 : -1;
    options.sort[paginationInput.sort] = sortOrder;
  } else {
    options.sort.createdAt = -1; // Default sort
  }

  const result = await this.yourModel.paginate(filter, options);

  return {
    docs: result.docs,
    pagination: {
      totalDocs: result.totalDocs,
      limit: result.limit,
      page: result.page,
      totalPages: result.totalPages,
      prevPage: result.prevPage,
      nextPage: result.nextPage,
      hasPrevPage: result.hasPrevPage,
      hasNextPage: result.hasNextPage,
      pagingCounter: result.pagingCounter,
    },
  };
}
```

### 3. Creating Paginated Types for New Entities

```typescript
// 1. Create paginated DTO
import { ObjectType } from '@nestjs/graphql';
import { createPaginatedType } from 'src/common/pagination.dto';
import { YourEntity } from '../entities/your-entity.entity';

@ObjectType()
export class PaginatedYourEntity extends createPaginatedType(YourEntity) {}

// 2. Update your input DTO
@InputType()
export class YourEntityInput {
  // Your existing filters...
  
  @Field(() => PaginationInput, { nullable: true })
  @IsOptional()
  @ValidateNested()
  @Type(() => PaginationInput)
  pagination?: PaginationInput;
}
```

### 4. Advanced Pagination Options

The mongoose-paginate-v2 plugin supports many options:

```typescript
const options = {
  page: 1,
  limit: 10,
  sort: { createdAt: -1 },
  populate: 'relatedField',
  select: 'field1 field2',
  lean: true, // Return plain objects instead of Mongoose documents
  leanWithId: false,
  offset: 0, // Alternative to page
  customLabels: {
    totalDocs: 'itemCount',
    docs: 'itemsList',
    limit: 'perPage',
    page: 'currentPage',
    nextPage: 'next',
    prevPage: 'prev',
    totalPages: 'pageCount',
    hasPrevPage: 'hasPrev',
    hasNextPage: 'hasNext',
    pagingCounter: 'slNo',
    meta: 'paginator'
  }
};
```

## Configuration

The plugin is already configured globally in `app.module.ts`:

```typescript
// Global plugin registration
mongoose.plugin(mongoosePaginate);
```

## Validation Rules

- `page`: Must be a positive integer ≥ 1
- `limit`: Must be a positive integer between 1 and 100
- `sort`: Any valid field name from your schema
- `sortOrder`: Either 'asc' or 'desc'

## Response Structure

```typescript
{
  docs: T[],           // Array of documents
  pagination: {
    totalDocs: number,    // Total number of documents
    limit: number,        // Documents per page
    page: number,         // Current page
    totalPages: number,   // Total number of pages
    prevPage?: number,    // Previous page number
    nextPage?: number,    // Next page number
    hasPrevPage: boolean, // Has previous page
    hasNextPage: boolean, // Has next page
    pagingCounter?: number // Starting index
  }
}
```
