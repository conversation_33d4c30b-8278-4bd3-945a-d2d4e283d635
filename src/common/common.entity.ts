import {
  InputType,
  ObjectType,
  registerEnumType,
  ID,
  Field,
} from '@nestjs/graphql';
import { Prop, Schema, Virtual } from '@nestjs/mongoose';

@ObjectType()
@Schema({ timestamps: true })
export class MongooseSchema {
  /** MongoDB ObjectId */
  @Field(() => ID)
  _id: string;

  @Virtual({
    get(this: MongooseSchema) {
      return this._id.toString();
    },
  })
  /** String representation of the MongoDB ObjectId */
  @Field(() => ID)
  id: string;

  /** Document creation timestamp */
  @Field()
  createdAt: Date;

  /** Document last update timestamp */
  @Field()
  updatedAt: Date;
}

@ObjectType()
export class Coordinates {
  /** Latitude */
  @Field()
  latitude: number;

  /** Longitude */
  @Field()
  longitude: number;
}

@ObjectType()
export class Address {
  /** Address */
  @Field()
  @Prop({ required: true })
  address: string;

  /** Coordinates */
  @Field()
  @Prop({ required: true, type: Object })
  coordinates: Coordinates;

  /** Metro line */
  @Field({ nullable: true })
  @Prop()
  metroLine?: string;

  /** Metro station */
  @Field({ nullable: true })
  @Prop()
  metroStation?: string;
}

export enum DayOfWeek {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}
registerEnumType(DayOfWeek, { name: 'DayOfWeek' });

/**Inputs */
@InputType()
export class CoordinatesInput {
  /** Latitude */
  @Field()
  latitude: number;

  /** Longitude */
  @Field()
  longitude: number;
}

@InputType()
export class AddressInput {
  /** Address */
  @Field()
  address: string;

  /** Coordinates */
  @Field()
  coordinates: CoordinatesInput;

  /** Metro line */
  @Field({ nullable: true })
  metroLine?: string;

  /** Metro station */
  @Field({ nullable: true })
  metroStation?: string;
}

@InputType()
export class DayTimingInput {
  /** Day of the week */
  @Field(() => DayOfWeek)
  day: DayOfWeek;

  /** Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc. */
  @Field(() => [Number])
  timings: number[];
}
