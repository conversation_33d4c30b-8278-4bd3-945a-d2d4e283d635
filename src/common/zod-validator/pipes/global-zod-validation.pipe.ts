import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ZodSchema, ZodError } from 'zod';
import { ZOD_SCHEMA_KEY } from '../index';

@Injectable()
export class GlobalZodValidationPipe implements PipeTransform {
  constructor(private reflector: Reflector) {}

  transform(value: any, metadata: ArgumentMetadata) {
    console.log('metadata types', metadata.type);

    // Only validate body, query, and param data
    if (!['body', 'query', 'param'].includes(metadata.type)) {
      return value as Record<string, unknown>;
    }

    // Get the Zod schema from metadata
    const zodSchema = this.reflector.get<ZodSchema>(
      ZOD_SCHEMA_KEY,
      metadata.metatype as NewableFunction,
    );

    if (!zodSchema) {
      return value as Record<string, unknown>;
    }

    try {
      return zodSchema.parse(value) as Record<string, unknown>;
    } catch (error) {
      if (error instanceof ZodError) {
        const errors = error.errors.map((err) => ({
          field: err.path.join('.') || metadata.data,
          message: err.message,
          code: err.code,
        }));

        throw new BadRequestException({
          message: 'Validation failed',
          statusCode: 400,
          errors,
        });
      }
      throw new BadRequestException('Validation failed');
    }
  }
}
